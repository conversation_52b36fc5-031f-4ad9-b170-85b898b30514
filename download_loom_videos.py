#!/usr/bin/env python3
"""
Script to download Loom videos using yt-dlp
"""

import subprocess
import os

def download_loom_video(url, description, index):
    """Download a single Loom video using yt-dlp"""
    print(f"\n[{index}] Downloading: {description}")
    print(f"URL: {url}")
    
    try:
        # Create a safe filename from the description
        safe_filename = "".join(c for c in description if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_filename = safe_filename.replace(' ', '_')
        
        # yt-dlp command with output template
        cmd = [
            'yt-dlp',
            '--output', f'{index:02d}_{safe_filename}.%(ext)s',
            '--format', 'best',
            '--write-info-json',
            '--write-description',
            url
        ]
        
        print(f"Command: {' '.join(cmd)}")
        print("Executing download...")
        
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print(f"✅ Successfully downloaded: {description}")
        else:
            print(f"❌ Failed to download (return code: {result.returncode})")
            # Try alternative format
            print(f"🔄 Trying alternative format...")
            alt_cmd = [
                'yt-dlp',
                '--output', f'{index:02d}_{safe_filename}_alt.%(ext)s',
                '--format', 'worst',
                url
            ]
            print(f"Alternative command: {' '.join(alt_cmd)}")
            alt_result = subprocess.run(alt_cmd, capture_output=False, text=True)
            if alt_result.returncode == 0:
                print(f"✅ Successfully downloaded with alternative format")
            else:
                print(f"❌ Alternative method also failed (return code: {alt_result.returncode})")
                    
    except Exception as e:
        print(f"❌ Error downloading {url}: {str(e)}")

def main():
    # Loom videos to download
    loom_videos = [
        {
            "url": "https://www.loom.com/share/61f5e9435f1d4ebb8a8e8de6201e7609?sid=b1b6b3b0-3583-4f6b-8e57-ab3dda92bd58",
            "description": "Onboarding Call Overview video"
        },
        {
            "url": "https://www.loom.com/share/ac41d9a0efff408b81b0d2598e590172?sid=6e7dd3fe-9948-442c-bf91-adf07cea9689",
            "description": "How to import leads in High Level"
        },
        {
            "url": "https://www.loom.com/share/55b685f18bd04423aca700807275f772?sid=dad50db8-033b-4cfa-8665-2ef5e4621a0c",
            "description": "How to sort leads in high level (After they respond to sms)"
        }
    ]
    
    print("Downloading Loom videos using yt-dlp...")
    print("=" * 60)
    
    # Create or navigate to downloads directory
    downloads_dir = "downloaded_files"
    if not os.path.exists(downloads_dir):
        os.makedirs(downloads_dir)
    
    # Create subdirectory for videos
    videos_dir = os.path.join(downloads_dir, "loom_videos")
    if not os.path.exists(videos_dir):
        os.makedirs(videos_dir)
    
    # Change to videos directory
    os.chdir(videos_dir)
    print(f"Working directory: {os.getcwd()}")
    
    # Check if yt-dlp is available
    try:
        subprocess.run(['yt-dlp', '--version'], capture_output=True, check=True)
        print("✅ yt-dlp is available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ yt-dlp is not installed or not in PATH")
        print("Please install yt-dlp first: pip install yt-dlp")
        return
    
    # Download each video
    for i, video_info in enumerate(loom_videos, 1):
        download_loom_video(video_info["url"], video_info["description"], i)
    
    print("\n" + "=" * 60)
    print("Loom video downloads completed!")
    print(f"Videos downloaded to: {os.getcwd()}")

if __name__ == "__main__":
    main()
