#!/usr/bin/env python3
"""
Script to download additional Google Drive links
"""

import subprocess
import os

def download_link(url, description, index):
    """Download a single Google Drive link"""
    print(f"\n[{index}] Downloading: {description}")
    print(f"URL: {url}")
    
    try:
        if '/folders/' in url:
            # It's a folder
            folder_id = url.split('/folders/')[1].split('?')[0]
            cmd = ['gdown', '--folder', '--remaining-ok', f'https://drive.google.com/drive/folders/{folder_id}']
            print(f"Command: {' '.join(cmd)}")
        elif '/file/d/' in url:
            # It's a single file
            file_id = url.split('/file/d/')[1].split('/')[0]
            cmd = ['gdown', f'https://drive.google.com/uc?id={file_id}']
            print(f"Command: {' '.join(cmd)}")
        else:
            print("❌ Unrecognized URL format")
            return
        
        print("Executing download...")
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print(f"✅ Successfully downloaded: {description}")
        else:
            print(f"❌ Failed to download (return code: {result.returncode})")
            
    except Exception as e:
        print(f"❌ Error downloading {url}: {str(e)}")

def main():
    # Additional links to download
    additional_links = [
        {
            "url": "https://drive.google.com/drive/folders/1vtgJgmrc3v_lTzRCeuogPqm1AK25ll4R?usp=drive_link",
            "description": "Call recordings Zeke examples"
        },
        {
            "url": "https://drive.google.com/drive/folders/1DWc-paQ5GyWJNno4qGEn_c71WV6myRDd?usp=sharing",
            "description": "Recorded offer (if they ask how it all works/ give more info in depth)"
        },
        {
            "url": "https://drive.google.com/file/d/1X0rQYPZ1HhynMV5YYohKsySalJ_McowN/view?usp=sharing",
            "description": "If they ask for price"
        }
    ]
    
    print("Downloading additional Google Drive links...")
    print("=" * 60)
    
    # Create or navigate to downloads directory
    downloads_dir = "downloaded_files"
    if not os.path.exists(downloads_dir):
        os.makedirs(downloads_dir)
    
    # Change to downloads directory
    os.chdir(downloads_dir)
    print(f"Working directory: {os.getcwd()}")
    
    # Download each link
    for i, link_info in enumerate(additional_links, 1):
        download_link(link_info["url"], link_info["description"], i)
    
    print("\n" + "=" * 60)
    print("Additional downloads completed!")
    print(f"Files downloaded to: {os.getcwd()}")

if __name__ == "__main__":
    main()
