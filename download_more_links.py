#!/usr/bin/env python3
"""
Script to download more Google Drive and Docs links
"""

import subprocess
import os
import re

def extract_file_id(url):
    """Extract file ID from Google Drive/Docs URL"""
    if 'drive.google.com' in url:
        if '/file/d/' in url:
            # File URL
            match = re.search(r'/file/d/([a-zA-Z0-9-_]+)', url)
            return match.group(1) if match else None
    elif 'docs.google.com' in url:
        # Document, spreadsheet, or presentation URL
        match = re.search(r'/d/([a-zA-Z0-9-_]+)', url)
        return match.group(1) if match else None
    return None

def get_file_type(url):
    """Determine the type of Google file"""
    if 'drive.google.com' in url:
        return 'file'
    elif 'docs.google.com/document' in url:
        return 'document'
    elif 'docs.google.com/spreadsheets' in url:
        return 'spreadsheet'
    elif 'docs.google.com/presentation' in url:
        return 'presentation'
    return 'unknown'

def download_link(url, description, index):
    """Download a single Google Drive link"""
    print(f"\n[{index}] Downloading: {description}")
    print(f"URL: {url}")
    
    file_id = extract_file_id(url)
    file_type = get_file_type(url)
    
    if not file_id:
        print("❌ Could not extract file ID from URL")
        return
    
    try:
        if file_type == 'document':
            # Download Google Doc as PDF
            download_url = f'https://docs.google.com/document/d/{file_id}/export?format=pdf'
            filename = f'document_extra_{index:02d}_{file_id}.pdf'
            cmd = ['gdown', download_url, '-O', filename]
        elif file_type == 'spreadsheet':
            # Download Google Sheet as PDF
            download_url = f'https://docs.google.com/spreadsheets/d/{file_id}/export?format=pdf'
            filename = f'spreadsheet_extra_{index:02d}_{file_id}.pdf'
            cmd = ['gdown', download_url, '-O', filename]
        elif file_type == 'presentation':
            # Download Google Presentation as PDF
            download_url = f'https://docs.google.com/presentation/d/{file_id}/export?format=pdf'
            filename = f'presentation_extra_{index:02d}_{file_id}.pdf'
            cmd = ['gdown', download_url, '-O', filename]
        else:
            # Regular Drive file
            filename = f'file_extra_{index:02d}_{file_id}'
            cmd = ['gdown', f'https://drive.google.com/uc?id={file_id}', '-O', filename]
        
        print(f"Command: {' '.join(cmd)}")
        print("Executing download...")
        
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print(f"✅ Successfully downloaded: {description}")
        else:
            print(f"❌ Failed to download (return code: {result.returncode})")
            # Try alternative method
            print(f"🔄 Trying alternative download method...")
            alt_cmd = ['gdown', '--fuzzy', url]
            print(f"Alternative command: {' '.join(alt_cmd)}")
            alt_result = subprocess.run(alt_cmd, capture_output=False, text=True)
            if alt_result.returncode == 0:
                print(f"✅ Successfully downloaded with alternative method")
            else:
                print(f"❌ Alternative method also failed (return code: {alt_result.returncode})")
                    
    except Exception as e:
        print(f"❌ Error downloading {url}: {str(e)}")

def main():
    # Additional links to download
    additional_links = [
        {
            "url": "https://docs.google.com/document/d/1QUEBT-lTlD7IBIY-pJD4UmQIPfFQjvG6cK6Tqwsdbak/edit?tab=t.0",
            "description": "Google Document 1"
        },
        {
            "url": "https://docs.google.com/document/d/1MuyjooUNkXu23cooYMtUiS__ONq9hB4UW9E4jN623C8/edit?tab=t.0",
            "description": "Google Document 2"
        },
        {
            "url": "https://drive.google.com/file/d/196FrylIEMwSincXEKx_56_irGV1_XOgJ/view?usp=drivesdk",
            "description": "Google Drive File 1"
        },
        {
            "url": "https://drive.google.com/file/d/1GdrhX4oiOVY2-OOWIPKGBugQUcxPk6XF/view?usp=drivesdk",
            "description": "Google Drive File 2"
        }
    ]
    
    print("Downloading additional Google Drive and Docs links...")
    print("=" * 60)
    
    # Create or navigate to downloads directory
    downloads_dir = "downloaded_files"
    if not os.path.exists(downloads_dir):
        os.makedirs(downloads_dir)
    
    # Change to downloads directory
    os.chdir(downloads_dir)
    print(f"Working directory: {os.getcwd()}")
    
    # Download each link
    for i, link_info in enumerate(additional_links, 1):
        download_link(link_info["url"], link_info["description"], i)
    
    print("\n" + "=" * 60)
    print("Additional downloads completed!")
    print(f"Files downloaded to: {os.getcwd()}")

if __name__ == "__main__":
    main()
